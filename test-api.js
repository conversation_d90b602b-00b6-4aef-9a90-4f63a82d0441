// Simple test script to test category API endpoints
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testCategoryAPI() {
    try {
        console.log('Testing Category API endpoints...');
        
        // Test GET categories (should require auth)
        console.log('\n1. Testing GET /api/categories (without auth)...');
        const getResponse = await fetch(`${BASE_URL}/api/categories`);
        console.log('Status:', getResponse.status);
        const getResult = await getResponse.json();
        console.log('Response:', getResult);
        
        // Test with a specific category ID for PUT
        const categoryId = '686d599c4d0dc7749b7aee95'; // needs category from logs
        
        console.log('\n2. Testing PUT /api/categories/:id (without auth)...');
        const putResponse = await fetch(`${BASE_URL}/api/categories/${categoryId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                displayName: 'Updated Needs',
                color: '#ff0000',
                percentage: 30
            })
        });
        console.log('Status:', putResponse.status);
        const putResult = await putResponse.json();
        console.log('Response:', putResult);
        
        console.log('\n3. Testing DELETE /api/categories/:id (without auth)...');
        const deleteResponse = await fetch(`${BASE_URL}/api/categories/${categoryId}`, {
            method: 'DELETE'
        });
        console.log('Status:', deleteResponse.status);
        const deleteResult = await deleteResponse.json();
        console.log('Response:', deleteResult);
        
    } catch (error) {
        console.error('Error testing API:', error);
    }
}

testCategoryAPI();
