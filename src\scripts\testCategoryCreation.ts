import mongoose from 'mongoose';
import { Category } from '../models';
import { config } from '../config/env';

async function testCategoryCreation() {
  try {
    console.log('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    console.log('Connected to database');

    // Test the static method
    console.log('Testing getDefaultCategories static method...');
    const defaultCategories = (Category as any).getDefaultCategories();
    console.log('Default categories:', defaultCategories);
    console.log('Number of default categories:', defaultCategories.length);

    // Test creating categories for a test user
    const testUserId = '686c112933b36ae35c3d32f5'; // Use the actual user ID from logs
    
    console.log('Checking existing categories for user...');
    const existingCategories = await Category.find({ userId: testUserId });
    console.log('Existing categories:', existingCategories.length);

    if (existingCategories.length === 0) {
      console.log('Creating default categories...');
      const categories = defaultCategories.map((cat: any) => ({
        ...cat,
        userId: testUserId,
      }));

      console.log('Categories to create:', categories);
      const createdCategories = await Category.insertMany(categories);
      console.log('Created categories:', createdCategories.length);
    } else {
      console.log('Categories already exist for this user');
      existingCategories.forEach(cat => {
        console.log(`- ${cat.name}: ${cat.displayName} (${cat.percentage}%)`);
      });
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from database');
  }
}

testCategoryCreation();
