import mongoose from 'mongoose';
import { User, Category, Transaction, Income, Need, Want, Investment, Donation } from '../models';
import { config } from '../config/env';

// Migration script to convert legacy category data to new unified system
async function migrateLegacyData() {
  try {
    console.log('Starting legacy data migration...');
    
    // Connect to database
    await mongoose.connect(config.MONGODB_URI);
    console.log('Connected to database');

    // Get all users
    const users = await User.find({});
    console.log(`Found ${users.length} users to migrate`);

    for (const user of users) {
      console.log(`\nMigrating user: ${user.email} (${user._id})`);
      
      // Check if user already has categories
      const existingCategories = await Category.find({ userId: user._id });
      if (existingCategories.length > 0) {
        console.log(`  User already has ${existingCategories.length} categories, skipping...`);
        continue;
      }

      // Create default categories for the user
      const defaultCategories = (Category as any).getDefaultCategories();
      const userCategories = defaultCategories.map((cat: any) => ({
        ...cat,
        userId: user._id,
      }));

      const createdCategories = await Category.insertMany(userCategories);
      console.log(`  Created ${createdCategories.length} default categories`);

      // Create a mapping of category names to IDs
      const categoryMap = new Map();
      createdCategories.forEach(cat => {
        categoryMap.set(cat.name, cat._id);
      });

      // Migrate legacy transactions
      let totalMigrated = 0;

      // Migrate Income transactions
      const incomes = await Income.find({ userId: user._id });
      if (incomes.length > 0) {
        const incomeTransactions = incomes.map(income => ({
          userId: user._id,
          categoryId: categoryMap.get('income'),
          amount: income.amount,
          description: income.description,
          date: income.date,
          createdAt: income.createdAt,
          updatedAt: income.updatedAt,
        }));
        await Transaction.insertMany(incomeTransactions);
        console.log(`  Migrated ${incomes.length} income transactions`);
        totalMigrated += incomes.length;
      }

      // Migrate Need transactions
      const needs = await Need.find({ userId: user._id });
      if (needs.length > 0) {
        const needTransactions = needs.map(need => ({
          userId: user._id,
          categoryId: categoryMap.get('needs'),
          amount: need.amount,
          description: need.description,
          date: need.date,
          createdAt: need.createdAt,
          updatedAt: need.updatedAt,
        }));
        await Transaction.insertMany(needTransactions);
        console.log(`  Migrated ${needs.length} need transactions`);
        totalMigrated += needs.length;
      }

      // Migrate Want transactions
      const wants = await Want.find({ userId: user._id });
      if (wants.length > 0) {
        const wantTransactions = wants.map(want => ({
          userId: user._id,
          categoryId: categoryMap.get('wants'),
          amount: want.amount,
          description: want.description,
          date: want.date,
          createdAt: want.createdAt,
          updatedAt: want.updatedAt,
        }));
        await Transaction.insertMany(wantTransactions);
        console.log(`  Migrated ${wants.length} want transactions`);
        totalMigrated += wants.length;
      }

      // Migrate Investment transactions
      const investments = await Investment.find({ userId: user._id });
      if (investments.length > 0) {
        const investmentTransactions = investments.map(investment => ({
          userId: user._id,
          categoryId: categoryMap.get('investments'),
          amount: investment.amount,
          description: investment.description,
          date: investment.date,
          createdAt: investment.createdAt,
          updatedAt: investment.updatedAt,
        }));
        await Transaction.insertMany(investmentTransactions);
        console.log(`  Migrated ${investments.length} investment transactions`);
        totalMigrated += investments.length;
      }

      // Migrate Donation transactions
      const donations = await Donation.find({ userId: user._id });
      if (donations.length > 0) {
        const donationTransactions = donations.map(donation => ({
          userId: user._id,
          categoryId: categoryMap.get('donations'),
          amount: donation.amount,
          description: donation.description,
          date: donation.date,
          createdAt: donation.createdAt,
          updatedAt: donation.updatedAt,
        }));
        await Transaction.insertMany(donationTransactions);
        console.log(`  Migrated ${donations.length} donation transactions`);
        totalMigrated += donations.length;
      }

      console.log(`  Total transactions migrated: ${totalMigrated}`);
    }

    console.log('\nMigration completed successfully!');
    console.log('\nNOTE: Legacy collections (Income, Need, Want, Investment, Donation) are still intact.');
    console.log('You can safely remove them after verifying the migration was successful.');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from database');
  }
}

// Cleanup function to remove legacy collections (run only after verifying migration)
async function cleanupLegacyCollections() {
  try {
    console.log('Starting cleanup of legacy collections...');
    
    await mongoose.connect(config.MONGODB_URI);
    console.log('Connected to database');

    // Drop legacy collections
    const collections = ['incomes', 'needs', 'wants', 'investments', 'donations'];
    
    for (const collectionName of collections) {
      try {
        await mongoose.connection.db!.dropCollection(collectionName);
        console.log(`Dropped collection: ${collectionName}`);
      } catch (error: any) {
        if (error.message.includes('ns not found')) {
          console.log(`Collection ${collectionName} does not exist, skipping...`);
        } else {
          throw error;
        }
      }
    }

    console.log('Legacy collections cleanup completed!');
    
  } catch (error) {
    console.error('Cleanup failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from database');
  }
}

// Check command line arguments
const command = process.argv[2];

if (command === 'migrate') {
  migrateLegacyData();
} else if (command === 'cleanup') {
  cleanupLegacyCollections();
} else {
  console.log('Usage:');
  console.log('  npm run migrate:legacy migrate   - Migrate legacy data to new system');
  console.log('  npm run migrate:legacy cleanup   - Remove legacy collections (run after verifying migration)');
  process.exit(1);
}
