// Categories management functionality
let categories = [];
let maxCategories = 5;
let customCategoriesCount = 0;
let canAddMore = true;
let editingCategoryId = null;

// DOM elements
let addCategoryBtn, categoryModal, deleteModal, categoryForm, categoriesContainer;
let categoryColor, categoryColorText, categoryIcon, iconPreview, categoryPercentage;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    loadCategories();
    setupEventListeners();
});

function initializeElements() {
    addCategoryBtn = document.getElementById('addCategoryBtn');
    categoryForm = document.getElementById('categoryForm');
    categoriesContainer = document.getElementById('categoriesContainer');

    const categoryModalElement = document.getElementById('categoryModal');
    const deleteModalElement = document.getElementById('deleteModal');

    if (categoryModalElement) {
        categoryModal = new bootstrap.Modal(categoryModalElement);
    }
    if (deleteModalElement) {
        deleteModal = new bootstrap.Modal(deleteModalElement);
    }

    categoryColor = document.getElementById('categoryColor');
    categoryColorText = document.getElementById('categoryColorText');
    categoryIcon = document.getElementById('categoryIcon');
    iconPreview = document.getElementById('iconPreview');
    categoryPercentage = document.getElementById('categoryPercentage');
}

function setupEventListeners() {
    // Add category button
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', () => openCategoryModal());
    }

    // Form submission
    if (categoryForm) {
        categoryForm.addEventListener('submit', handleFormSubmit);
    }
    
    // Color picker sync
    if (categoryColor && categoryColorText) {
        categoryColor.addEventListener('input', (e) => {
            categoryColorText.value = e.target.value;
        });

        categoryColorText.addEventListener('input', (e) => {
            if (/^#[0-9A-Fa-f]{6}$/.test(e.target.value)) {
                categoryColor.value = e.target.value;
            }
        });
    }

    // Icon preview
    if (categoryIcon && iconPreview) {
        categoryIcon.addEventListener('input', (e) => {
            iconPreview.className = e.target.value || 'fas fa-folder';
        });
    }

    // Percentage calculation
    if (categoryPercentage) {
        categoryPercentage.addEventListener('input', updateRemainingPercentage);
    }

    // Delete confirmation
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', handleDeleteCategory);
    }
}

async function loadCategories() {
    try {
        if (categoriesContainer) {
            showLoading(categoriesContainer);
        }

        const response = await fetch('/api/categories', {
            credentials: 'include'
        });
        if (!response.ok) {
            throw new Error('Failed to load categories');
        }

        const data = await response.json();
        categories = data.data || [];
        maxCategories = data.maxAllowed || 5;
        customCategoriesCount = data.customCategoriesCount || 0;
        canAddMore = data.canAddMore !== false;

        updateCategoryInfo();
        renderCategories();

    } catch (error) {
        console.error('Error loading categories:', error);
        if (categoriesContainer) {
            categoriesContainer.innerHTML = '<div class="alert alert-danger">Failed to load categories</div>';
        }
    }
}

function updateCategoryInfo() {
    const maxCategoriesEl = document.getElementById('maxCategories');
    const maxCategoriesTextEl = document.getElementById('maxCategoriesText');
    const categoryCountEl = document.getElementById('categoryCount');

    if (maxCategoriesEl) maxCategoriesEl.textContent = maxCategories;
    if (maxCategoriesTextEl) maxCategoriesTextEl.textContent = maxCategories;
    if (categoryCountEl) categoryCountEl.textContent = `${customCategoriesCount}/${maxCategories} custom categories`;

    // Enable/disable add button based on custom categories limit (excluding income)
    if (addCategoryBtn) {
        addCategoryBtn.disabled = !canAddMore;
        if (!canAddMore) {
            addCategoryBtn.innerHTML = '<i class="fas fa-ban me-2"></i>Category Limit Reached';
        } else {
            addCategoryBtn.innerHTML = '<i class="fas fa-plus me-2"></i>Add Category';
        }
    }
}

function renderCategories() {
    if (!categoriesContainer) {
        return;
    }

    if (categories.length === 0) {
        categoriesContainer.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No categories found</h5>
                <p class="text-muted">Create your first category to get started</p>
            </div>
        `;
        return;
    }
    
    const totalPercentage = categories.reduce((sum, cat) => sum + cat.percentage, 0);
    
    categoriesContainer.innerHTML = categories.map(category => `
        <div class="category-item border rounded p-3 mb-3" data-category-id="${category._id}">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <div class="category-color me-3" style="background-color: ${category.color}; width: 20px; height: 20px; border-radius: 50%;"></div>
                        <div>
                            <h6 class="mb-1">
                                <i class="${category.icon} me-2"></i>${category.displayName}
                            </h6>
                            <small class="text-muted">${category.name}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h5 mb-0">${category.percentage}%</div>
                        <small class="text-muted">Budget Allocation</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2 justify-content-end">
                        ${category.name === 'income' ? `
                            <span class="badge bg-success">Default Income</span>
                        ` : `
                            <button class="btn btn-sm btn-outline-primary" onclick="editCategory('${category._id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory('${category._id}', '${category.displayName}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        `}
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    // Add total percentage indicator
    const percentageClass = totalPercentage === 100 ? 'success' : totalPercentage > 100 ? 'danger' : 'warning';
    categoriesContainer.innerHTML += `
        <div class="alert alert-${percentageClass} mt-3">
            <i class="fas fa-calculator me-2"></i>
            Total Budget Allocation: <strong>${totalPercentage}%</strong>
            ${totalPercentage !== 100 ? ` (${totalPercentage > 100 ? 'Over' : 'Under'} budget by ${Math.abs(100 - totalPercentage)}%)` : ''}
        </div>
    `;
}

function openCategoryModal(categoryId = null) {
    editingCategoryId = categoryId;
    const isEdit = !!categoryId;

    document.getElementById('categoryModalTitle').textContent = isEdit ? 'Edit Category' : 'Add Category';
    document.getElementById('saveCategoryBtn').innerHTML = `<i class="fas fa-save me-2"></i>${isEdit ? 'Update' : 'Save'} Category`;
    
    if (isEdit) {
        const category = categories.find(c => c._id === categoryId);
        if (category) {
            document.getElementById('categoryId').value = category._id;
            document.getElementById('categoryName').value = category.name;
            document.getElementById('categoryDisplayName').value = category.displayName;
            document.getElementById('categoryColor').value = category.color;
            document.getElementById('categoryColorText').value = category.color;
            document.getElementById('categoryIcon').value = category.icon;
            document.getElementById('categoryPercentage').value = category.percentage;
            iconPreview.className = category.icon;
            
            // Disable name editing for default categories
            document.getElementById('categoryName').disabled = category.isDefault;
        }
    } else {
        categoryForm.reset();
        document.getElementById('categoryColor').value = '#007bff';
        document.getElementById('categoryColorText').value = '#007bff';
        document.getElementById('categoryIcon').value = 'fas fa-folder';
        iconPreview.className = 'fas fa-folder';
        document.getElementById('categoryName').disabled = false;
    }
    
    updateRemainingPercentage();
    if (categoryModal) {
        console.log('Showing category modal');
        categoryModal.show();
    } else {
        console.error('Category modal not initialized');
    }
}

function updateRemainingPercentage() {
    const currentPercentage = parseFloat(categoryPercentage.value) || 0;
    const otherCategoriesTotal = categories
        .filter(c => c._id !== editingCategoryId)
        .reduce((sum, cat) => sum + cat.percentage, 0);
    
    const remaining = 100 - otherCategoriesTotal - currentPercentage;
    const remainingSpan = document.getElementById('remainingPercentage');
    
    if (remaining < 0) {
        remainingSpan.innerHTML = `<span class="text-danger">Over budget by ${Math.abs(remaining)}%</span>`;
    } else {
        remainingSpan.innerHTML = `Remaining: ${remaining}%`;
    }
}

async function handleFormSubmit(e) {
    e.preventDefault();

    const formData = new FormData(categoryForm);
    const data = {
        name: formData.get('name'),
        displayName: formData.get('displayName'),
        color: formData.get('color'),
        icon: formData.get('icon'),
        percentage: parseFloat(formData.get('percentage')),
    };

    try {
        const isEdit = !!editingCategoryId;
        const url = isEdit ? `/api/categories/${editingCategoryId}` : '/api/categories';
        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(data),
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to save category');
        }

        showAlert('success', `Category ${isEdit ? 'updated' : 'created'} successfully!`);
        if (categoryModal) categoryModal.hide();
        loadCategories();

    } catch (error) {
        console.error('Error saving category:', error);
        showAlert('error', error.message);
    }
}

// Make functions global so they can be called from onclick attributes
window.editCategory = function(categoryId) {
    console.log('editCategory called with ID:', categoryId);
    openCategoryModal(categoryId);
};

window.deleteCategory = function(categoryId, categoryName) {
    console.log('deleteCategory called with ID:', categoryId, 'Name:', categoryName);
    const deleteNameEl = document.getElementById('deleteCategoryName');
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    if (deleteNameEl) deleteNameEl.textContent = categoryName;
    if (confirmBtn) confirmBtn.dataset.categoryId = categoryId;
    if (deleteModal) {
        console.log('Showing delete modal');
        deleteModal.show();
    } else {
        console.error('Delete modal not found');
    }
};

async function handleDeleteCategory() {
    const categoryId = document.getElementById('confirmDeleteBtn').dataset.categoryId;
    
    try {
        const response = await fetch(`/api/categories/${categoryId}`, {
            method: 'DELETE',
            credentials: 'include',
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to delete category');
        }
        
        showAlert('success', 'Category deleted successfully!');
        deleteModal.hide();
        loadCategories();
        
    } catch (error) {
        console.error('Error deleting category:', error);
        showAlert('error', error.message);
    }
}

// Utility functions
function showLoading(container) {
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

function showError(message) {
    showAlert('error', message);
}
