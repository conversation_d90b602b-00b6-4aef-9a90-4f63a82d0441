import { Response } from 'express';
import { z } from 'zod';
import { Transaction, Category } from '../models';
import { AuthenticatedRequest } from '../middlewares/auth';

// Validation schemas
const createTransactionSchema = z.object({
  categoryId: z.string().min(1, 'Category ID is required'),
  amount: z.number().min(0, 'Amount must be at least 0'),
  description: z.string().min(1, 'Description is required').max(500, 'Description must be less than 500 characters'),
  date: z.string().optional(),
});

const updateTransactionSchema = createTransactionSchema.partial();

export class NewTransactionController {
  // Get all transactions for the authenticated user
  static async getAll(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { page = 1, limit = 10, startDate, endDate, categoryId } = req.query as any;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = { userId: req.user.id };
      
      if (categoryId) {
        query.categoryId = categoryId;
      }
      
      if (startDate || endDate) {
        query.date = {};
        if (startDate) query.date.$gte = new Date(startDate);
        if (endDate) query.date.$lte = new Date(endDate);
      }

      const [transactions, total] = await Promise.all([
        Transaction
          .find(query)
          .populate('category', 'name displayName color icon')
          .sort({ date: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        Transaction.countDocuments(query),
      ]);

      res.json({
        data: transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      console.error('Error fetching transactions:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Get a specific transaction by ID
  static async getById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const transaction = await Transaction.findOne({
        _id: req.params.id,
        userId: req.user.id,
      }).populate('category', 'name displayName color icon');

      if (!transaction) {
        res.status(404).json({ error: 'Transaction not found' });
        return;
      }

      res.json({ data: transaction });
    } catch (error) {
      console.error('Error fetching transaction:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Create a new transaction
  static async create(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      // Validate request body
      const validationResult = createTransactionSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({
          error: 'Validation failed',
          details: validationResult.error.errors,
        });
        return;
      }

      const { categoryId, amount, description, date } = validationResult.data;

      // Verify that the category belongs to the user
      const category = await Category.findOne({
        _id: categoryId,
        userId: req.user.id,
      });

      if (!category) {
        res.status(400).json({ error: 'Invalid category' });
        return;
      }

      // Create the transaction
      const transaction = new Transaction({
        userId: req.user.id,
        categoryId,
        amount,
        description,
        date: date ? new Date(date) : new Date(),
      });

      await transaction.save();

      // Populate category information before returning
      await transaction.populate('category', 'name displayName color icon');

      res.status(201).json({
        message: 'Transaction created successfully',
        data: transaction,
      });
    } catch (error) {
      console.error('Error creating transaction:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Update an existing transaction
  static async update(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      // Validate request body
      const validationResult = updateTransactionSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({
          error: 'Validation failed',
          details: validationResult.error.errors,
        });
        return;
      }

      const updateData = validationResult.data;

      // If updating category, verify it belongs to the user
      if (updateData.categoryId) {
        const category = await Category.findOne({
          _id: updateData.categoryId,
          userId: req.user.id,
        });

        if (!category) {
          res.status(400).json({ error: 'Invalid category' });
          return;
        }
      }

      // Convert date string to Date object if provided
      if (updateData.date) {
        (updateData as any).date = new Date(updateData.date);
      }

      const transaction = await Transaction.findOneAndUpdate(
        { _id: req.params.id, userId: req.user.id },
        updateData,
        { new: true, runValidators: true }
      ).populate('category', 'name displayName color icon');

      if (!transaction) {
        res.status(404).json({ error: 'Transaction not found' });
        return;
      }

      res.json({
        message: 'Transaction updated successfully',
        data: transaction,
      });
    } catch (error) {
      console.error('Error updating transaction:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Delete a transaction
  static async delete(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const transaction = await Transaction.findOneAndDelete({
        _id: req.params.id,
        userId: req.user.id,
      });

      if (!transaction) {
        res.status(404).json({ error: 'Transaction not found' });
        return;
      }

      res.json({
        message: 'Transaction deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting transaction:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Get transactions by category name (for backward compatibility)
  static async getByCategoryName(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { categoryName } = req.params;
      const { page = 1, limit = 10, startDate, endDate } = req.query as any;
      const skip = (page - 1) * limit;

      // Find the category by name
      const category = await Category.findOne({
        userId: req.user.id,
        name: categoryName,
      });

      if (!category) {
        res.status(404).json({ error: 'Category not found' });
        return;
      }

      // Build query
      const query: any = { 
        userId: req.user.id,
        categoryId: category._id,
      };
      
      if (startDate || endDate) {
        query.date = {};
        if (startDate) query.date.$gte = new Date(startDate);
        if (endDate) query.date.$lte = new Date(endDate);
      }

      const [transactions, total] = await Promise.all([
        Transaction
          .find(query)
          .populate('category', 'name displayName color icon')
          .sort({ date: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        Transaction.countDocuments(query),
      ]);

      res.json({
        data: transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        category: {
          _id: category._id,
          name: category.name,
          displayName: category.displayName,
          color: category.color,
          icon: category.icon,
        },
      });
    } catch (error) {
      console.error('Error fetching transactions by category:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
