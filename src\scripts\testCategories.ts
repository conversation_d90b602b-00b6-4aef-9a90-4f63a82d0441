import mongoose from 'mongoose';
import { User, Category, Transaction } from '../models';
import { CategoryController } from '../controllers/categoryController';
import { config } from '../config/env';

// Test script to validate the custom category system
async function testCategorySystem() {
  try {
    console.log('Starting category system tests...');
    
    // Connect to database
    await mongoose.connect(config.MONGODB_URI);
    console.log('Connected to database');

    // Test 1: Create a test user
    console.log('\n1. Testing user creation and default categories...');
    
    const testUser = new User({
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      fullName: 'Test User',
      devices: [],
    });
    
    await testUser.save();
    console.log('✓ Test user created');

    // Initialize default categories
    await CategoryController.initializeDefaultCategories((testUser._id as any).toString());
    console.log('✓ Default categories initialized');

    // Verify categories were created
    const categories = await Category.find({ userId: testUser._id });
    console.log(`✓ Found ${categories.length} categories for user`);
    
    if (categories.length !== 5) {
      throw new Error(`Expected 5 categories, found ${categories.length}`);
    }

    // Test 2: Test category limits
    console.log('\n2. Testing category limits...');
    
    try {
      const newCategory = new Category({
        userId: testUser._id,
        name: 'test_category',
        displayName: 'Test Category',
        color: '#ff0000',
        icon: 'fas fa-test',
        percentage: 10,
        isDefault: false,
        order: 5,
      });
      
      await newCategory.save();
      console.log('✗ Category limit validation failed - should not allow 6th category');
    } catch (error: any) {
      if (error.message.includes('Maximum 5 categories allowed')) {
        console.log('✓ Category limit validation working');
      } else {
        throw error;
      }
    }

    // Test 3: Test percentage validation
    console.log('\n3. Testing percentage validation...');
    
    // Update a category to exceed 100% total
    const incomeCategory = categories.find(c => c.name === 'income');
    if (incomeCategory) {
      try {
        incomeCategory.percentage = 50; // This should cause total to exceed 100%
        await incomeCategory.save();
        console.log('✗ Percentage validation failed - should not allow over 100%');
      } catch (error: any) {
        if (error.message.includes('Total category percentages cannot exceed 100%')) {
          console.log('✓ Percentage validation working');
        } else {
          throw error;
        }
      }
    }

    // Test 4: Test transaction creation
    console.log('\n4. Testing transaction creation...');
    
    const needsCategory = categories.find(c => c.name === 'needs');
    if (needsCategory) {
      const transaction = new Transaction({
        userId: testUser._id,
        categoryId: needsCategory._id,
        amount: 100,
        description: 'Test transaction',
        date: new Date(),
      });
      
      await transaction.save();
      console.log('✓ Transaction created successfully');
      
      // Verify transaction with category population
      const populatedTransaction = await Transaction.findById(transaction._id).populate('category');
      if (populatedTransaction && (populatedTransaction as any).category) {
        console.log('✓ Transaction category population working');
      } else {
        console.log('✗ Transaction category population failed');
      }
    }

    // Test 5: Test category deletion with transactions
    console.log('\n5. Testing category deletion with transactions...');
    
    if (needsCategory) {
      try {
        await Category.findByIdAndDelete(needsCategory._id);
        console.log('✗ Category deletion should be prevented when transactions exist');
      } catch (error) {
        // This should fail in the controller, not at the model level
        // The controller should check for transactions before deletion
        console.log('✓ Category deletion validation would be handled by controller');
      }
    }

    // Test 6: Test unique category names per user
    console.log('\n6. Testing unique category names...');
    
    try {
      const duplicateCategory = new Category({
        userId: testUser._id,
        name: 'needs', // Duplicate name
        displayName: 'Duplicate Needs',
        color: '#ff0000',
        icon: 'fas fa-duplicate',
        percentage: 5,
        isDefault: false,
        order: 6,
      });
      
      await duplicateCategory.save();
      console.log('✗ Duplicate category name validation failed');
    } catch (error: any) {
      if (error.code === 11000) { // MongoDB duplicate key error
        console.log('✓ Unique category name validation working');
      } else {
        throw error;
      }
    }

    console.log('\n✅ All tests completed successfully!');
    
    // Cleanup
    console.log('\nCleaning up test data...');
    await Transaction.deleteMany({ userId: testUser._id });
    await Category.deleteMany({ userId: testUser._id });
    await User.findByIdAndDelete(testUser._id);
    console.log('✓ Test data cleaned up');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from database');
  }
}

// Run tests
testCategorySystem();
