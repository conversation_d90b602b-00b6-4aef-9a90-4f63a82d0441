import { Response } from 'express';
import { DashboardService } from '../services/dashboardService';
import { AuthenticatedRequest } from '../middlewares/auth';

export class DashboardController {
  static async getDashboard(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      console.log('API: Dashboard data requested by user:', req.user?.id);

      if (!req.user) {
        console.log('API: User not authenticated');
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { startDate, endDate } = req.query;

      const start = startDate ? new Date(startDate as string) : undefined;
      const end = endDate ? new Date(endDate as string) : undefined;

      console.log('API: Fetching dashboard data for user:', req.user.id);
      // Try to use dynamic dashboard first, fallback to legacy if no categories
      let dashboardData;
      try {
        dashboardData = await DashboardService.getDynamicDashboardData(
          req.user.id,
          start,
          end
        );
      } catch (error: any) {
        if (error.message.includes('No categories found')) {
          // Fallback to legacy dashboard for users without categories
          dashboardData = await DashboardService.getDashboardData(
            req.user.id,
            start,
            end
          );
        } else {
          throw error;
        }
      }

      console.log('API: Dashboard data retrieved successfully');
      res.json({
        message: 'Dashboard data retrieved successfully',
        data: dashboardData,
      });
    } catch (error) {
      console.error('Dashboard error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  static async getMonthlyTrends(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      console.log('API: Monthly trends requested by user:', req.user?.id);

      if (!req.user) {
        console.log('API: User not authenticated for trends');
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { months = 6 } = req.query;
      const monthsNumber = parseInt(months as string, 10);

      if (isNaN(monthsNumber) || monthsNumber < 1 || monthsNumber > 24) {
        console.log('API: Invalid months parameter:', months);
        res.status(400).json({ error: 'Months must be between 1 and 24' });
        return;
      }

      console.log('API: Fetching trends data for user:', req.user.id, 'months:', monthsNumber);
      // Try to use dynamic trends first, fallback to legacy if no categories
      let trendsData;
      try {
        trendsData = await DashboardService.getDynamicMonthlyTrends(
          req.user.id,
          monthsNumber
        );
      } catch (error: any) {
        if (error.message && error.message.includes('No categories found')) {
          // Fallback to legacy trends for users without categories
          trendsData = await DashboardService.getMonthlyTrends(
            req.user.id,
            monthsNumber
          );
        } else {
          throw error;
        }
      }

      console.log('API: Trends data retrieved successfully, count:', trendsData.length);
      res.json({
        message: 'Monthly trends retrieved successfully',
        data: trendsData,
      });
    } catch (error) {
      console.error('Monthly trends error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
