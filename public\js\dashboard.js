// Dashboard functionality
let budgetChart = null;
let trendsChart = null;

// Prevent multiple simultaneous loads
let isLoading = false;
let isLoadingTransactions = false;

document.addEventListener('DOMContentLoaded', function() {
    // Authentication is handled server-side, so we can directly load data
    loadDashboardData();
    loadCategoriesForQuickAdd();

    // Refresh button
    const refreshBtn = document.getElementById('refreshData');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            if (!isLoading) {
                loadDashboardData();
            }
        });
    }
});

async function loadDashboardData() {
    // Prevent multiple simultaneous loads
    if (isLoading) {
        console.log('Dashboard already loading, skipping...');
        return;
    }

    try {
        isLoading = true;
        showLoading(true);
        console.log('Loading dashboard data...');

        // Load real dashboard data from API
        console.log('Making API requests to /dashboard and /dashboard/trends');
        const dashboardResponse = await apiRequest('/dashboard');
        const trendsResponse = await apiRequest('/dashboard/trends');

        console.log('Dashboard response status:', dashboardResponse.status);
        console.log('Trends response status:', trendsResponse.status);

        if (dashboardResponse.ok && trendsResponse.ok) {
            const dashboardResult = await dashboardResponse.json();
            const trendsResult = await trendsResponse.json();

            // Extract data from the API response format
            const dashboardData = dashboardResult.data;
            const trendsData = trendsResult.data;

            console.log('Successfully loaded dashboard data:', dashboardData);
            console.log('Successfully loaded trends data:', trendsData);

            renderSummaryCards(dashboardData);
            renderBudgetChart(dashboardData);
            renderBudgetStatus(dashboardData);
            renderCategoryBreakdown(dashboardData);
            renderTrendsChart(trendsData);
            loadRecentTransactions();
        } else {
            // Fallback to mock data if API fails
            console.warn('API failed, using mock data. Dashboard status:', dashboardResponse.status, 'Trends status:', trendsResponse.status);

            // Log response details for debugging
            if (!dashboardResponse.ok) {
                const dashboardError = await dashboardResponse.text();
                console.error('Dashboard API error:', dashboardError);
            }
            if (!trendsResponse.ok) {
                const trendsError = await trendsResponse.text();
                console.error('Trends API error:', trendsError);
            }

            loadMockDashboardData();
        }

    } catch (error) {
        console.error('Dashboard error:', error);
        // Fallback to mock data on error
        console.warn('Error loading real data, using mock data');
        loadMockDashboardData();
    } finally {
        isLoading = false;
        showLoading(false);
        console.log('Dashboard loading completed');
    }
}

// Fallback function with mock data
function loadMockDashboardData() {
    const mockDashboardData = {
        totalIncome: 5000,
        totalSpent: 3500,
        totalRemaining: 1500,
        overallBudgetStatus: 'under',
        budgetAllocation: {
            needs: 1250,
            wants: 1250,
            investments: 2250,
            donations: 250
        },
        summary: {
            needs: {
                allocated: 1250,
                spent: 800,
                remaining: 450,
                percentUsed: 64.0,
                isOverBudget: false
            },
            wants: {
                allocated: 1250,
                spent: 600,
                remaining: 650,
                percentUsed: 48.0,
                isOverBudget: false
            },
            investments: {
                allocated: 2250,
                spent: 1800,
                remaining: 450,
                percentUsed: 80.0,
                isOverBudget: false
            },
            donations: {
                allocated: 250,
                spent: 300,
                remaining: -50,
                percentUsed: 120.0,
                isOverBudget: true
            }
        }
    };

    const mockTrendsData = [
        { year: 2024, month: 1, income: 4800, needs: 750, wants: 500, investments: 1600, donations: 200 },
        { year: 2024, month: 2, income: 5200, needs: 820, wants: 650, investments: 1700, donations: 250 },
        { year: 2024, month: 3, income: 4900, needs: 780, wants: 580, investments: 1650, donations: 280 },
        { year: 2024, month: 4, income: 5100, needs: 800, wants: 620, investments: 1750, donations: 300 },
        { year: 2024, month: 5, income: 5300, needs: 850, wants: 680, investments: 1800, donations: 320 },
        { year: 2024, month: 6, income: 5000, needs: 800, wants: 600, investments: 1800, donations: 300 }
    ];

    renderSummaryCards(mockDashboardData);
    renderBudgetChart(mockDashboardData);
    renderBudgetStatus(mockDashboardData);
    renderCategoryBreakdown(mockDashboardData);
    renderTrendsChart(mockTrendsData);
    loadRecentTransactionsMock();
}

function renderSummaryCards(data) {
    const container = document.getElementById('summaryCards');
    
    const cards = [
        {
            title: 'Total Income',
            value: formatCurrency(data.totalIncome),
            icon: 'fas fa-money-bill-wave',
            color: 'success'
        },
        {
            title: 'Total Spent',
            value: formatCurrency(data.totalSpent),
            icon: 'fas fa-shopping-cart',
            color: 'danger'
        },
        {
            title: 'Remaining Budget',
            value: formatCurrency(data.totalRemaining),
            icon: 'fas fa-piggy-bank',
            color: data.totalRemaining >= 0 ? 'info' : 'warning'
        },
        {
            title: 'Budget Status',
            value: data.overallBudgetStatus.charAt(0).toUpperCase() + data.overallBudgetStatus.slice(1),
            icon: 'fas fa-chart-pie',
            color: data.overallBudgetStatus === 'over' ? 'danger' : 'success'
        }
    ];
    
    container.innerHTML = cards.map(card => `
        <div class="col-md-6 col-lg-3 mb-3">
            <div class="card bg-${card.color} text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">${card.title}</h6>
                            <h4 class="mb-0">${card.value}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="${card.icon} fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function renderBudgetChart(data) {
    const ctx = document.getElementById('budgetChart').getContext('2d');
    
    if (budgetChart) {
        budgetChart.destroy();
    }
    
    budgetChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Needs', 'Wants', 'Investments', 'Donations'],
            datasets: [{
                data: [
                    data.summary.needs.allocated,
                    data.summary.wants.allocated,
                    data.summary.investments.allocated,
                    data.summary.donations.allocated
                ],
                backgroundColor: [
                    '#dc3545', // Red for needs
                    '#ffc107', // Yellow for wants
                    '#28a745', // Green for investments
                    '#17a2b8'  // Blue for donations
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = formatCurrency(context.parsed);
                            const percentage = ((context.parsed / data.totalIncome) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

function renderBudgetStatus(data) {
    const container = document.getElementById('budgetStatus');
    
    const statusColor = data.overallBudgetStatus === 'over' ? 'danger' : 
                       data.overallBudgetStatus === 'under' ? 'success' : 'warning';
    
    container.innerHTML = `
        <div class="text-center">
            <div class="badge bg-${statusColor} fs-6 mb-3">
                ${data.overallBudgetStatus.toUpperCase()} BUDGET
            </div>
            <p class="mb-2"><strong>Total Income:</strong> ${formatCurrency(data.totalIncome)}</p>
            <p class="mb-2"><strong>Total Spent:</strong> ${formatCurrency(data.totalSpent)}</p>
            <p class="mb-0"><strong>Remaining:</strong> ${formatCurrency(data.totalRemaining)}</p>
        </div>
    `;
}

function renderCategoryBreakdown(data) {
    const container = document.getElementById('categoryBreakdown');

    // Check if we have dynamic categories or legacy format
    let categories;
    if (data.categories && Array.isArray(data.categories)) {
        // New dynamic categories format
        categories = data.categories.filter(cat => cat.name !== 'income'); // Exclude income from breakdown
    } else {
        // Legacy format
        categories = [
            { name: 'needs', displayName: 'Needs', icon: 'fas fa-shopping-basket', color: '#dc3545' },
            { name: 'wants', displayName: 'Wants', icon: 'fas fa-gift', color: '#ffc107' },
            { name: 'investments', displayName: 'Investments', icon: 'fas fa-chart-line', color: '#28a745' },
            { name: 'donations', displayName: 'Donations', icon: 'fas fa-heart', color: '#17a2b8' }
        ];
    }

    container.innerHTML = categories.map(category => {
        const categoryData = data.summary[category.name];
        if (!categoryData) return ''; // Skip if no data for this category

        const progressPercentage = Math.min((categoryData.spent / categoryData.allocated) * 100, 100);
        const progressColor = categoryData.isOverBudget ? 'danger' : 'success';
        
        return `
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="card-title mb-0">
                                <i class="${category.icon} me-2"></i>${category.displayName || category.title}
                            </h6>
                            ${categoryData.isOverBudget ? '<span class="badge bg-danger">Over</span>' : ''}
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">Allocated: ${formatCurrency(categoryData.allocated)}</small>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">Spent: ${formatCurrency(categoryData.spent)}</small>
                        </div>
                        <div class="progress mb-2" style="height: 8px;">
                            <div class="progress-bar bg-${progressColor}" 
                                 style="width: ${progressPercentage}%"></div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">${formatPercentage(categoryData.percentUsed)} used</small>
                            <small class="text-muted">${formatCurrency(categoryData.remaining)} left</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function renderTrendsChart(data) {
    const ctx = document.getElementById('trendsChart').getContext('2d');
    
    if (trendsChart) {
        trendsChart.destroy();
    }
    
    const labels = data.map(item => {
        const date = new Date(item.year, item.month - 1);
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });
    
    trendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Income',
                    data: data.map(item => item.income),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Needs',
                    data: data.map(item => item.needs),
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Wants',
                    data: data.map(item => item.wants),
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Investments',
                    data: data.map(item => item.investments),
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });
}

async function loadRecentTransactions() {
    // Prevent multiple simultaneous loads
    if (isLoadingTransactions) {
        console.log('Recent transactions already loading, skipping...');
        return;
    }

    try {
        isLoadingTransactions = true;

        // Load recent transactions from all categories
        const categories = ['incomes', 'needs', 'wants', 'investments', 'donations'];
        const promises = categories.map(category =>
            apiRequest(`/${category}?limit=5&page=1`)
        );

        const responses = await Promise.all(promises);
        const allTransactions = [];

        for (let i = 0; i < responses.length; i++) {
            if (responses[i].ok) {
                const data = await responses[i].json();
                if (data.data && Array.isArray(data.data)) {
                    data.data.forEach(transaction => {
                        allTransactions.push({
                            ...transaction,
                            category: categories[i]
                        });
                    });
                }
            }
        }

        // Sort by date (newest first) and take top 10
        allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));
        const recentTransactions = allTransactions.slice(0, 10);

        renderRecentTransactions(recentTransactions);
    } catch (error) {
        console.error('Error loading recent transactions:', error);
        // Fallback to mock data
        loadRecentTransactionsMock();
    } finally {
        isLoadingTransactions = false;
    }
}

function renderRecentTransactions(transactions) {
    const tbody = document.querySelector('#recentTransactions tbody');
    
    if (transactions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted">No recent transactions</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = transactions.map(transaction => `
        <tr>
            <td>${formatDate(transaction.date)}</td>
            <td>
                <span class="badge bg-secondary">${transaction.category}</span>
            </td>
            <td>${transaction.description}</td>
            <td class="text-end">${formatCurrency(transaction.amount)}</td>
        </tr>
    `).join('');
}

function loadRecentTransactionsMock() {
    const mockTransactions = [
        { date: '2024-01-15', category: 'incomes', description: 'Salary Payment', amount: 5000 },
        { date: '2024-01-14', category: 'needs', description: 'Grocery Shopping', amount: 120 },
        { date: '2024-01-13', category: 'wants', description: 'Movie Tickets', amount: 25 },
        { date: '2024-01-12', category: 'investments', description: 'Stock Purchase', amount: 500 },
        { date: '2024-01-11', category: 'donations', description: 'Charity Donation', amount: 50 },
        { date: '2024-01-10', category: 'needs', description: 'Electricity Bill', amount: 80 },
        { date: '2024-01-09', category: 'wants', description: 'Coffee Shop', amount: 15 },
        { date: '2024-01-08', category: 'investments', description: 'Mutual Fund', amount: 300 }
    ];

    renderRecentTransactions(mockTransactions);
}

function showLoading(show) {
    const refreshBtn = document.getElementById('refreshData');
    if (refreshBtn) {
        if (show) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        } else {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
        }
    }
}

// Load categories for quick add dropdown
async function loadCategoriesForQuickAdd() {
    try {
        const response = await fetch('/api/categories');
        if (!response.ok) {
            // If categories API fails, keep the default hardcoded categories
            return;
        }

        const data = await response.json();
        const categories = data.data || [];

        if (categories.length === 0) {
            // No categories found, keep default
            return;
        }

        updateQuickAddDropdown(categories);

    } catch (error) {
        console.error('Error loading categories for quick add:', error);
        // Keep default categories on error
    }
}

function updateQuickAddDropdown(categories) {
    const dropdown = document.getElementById('dashboardQuickAddDropdown');
    if (!dropdown) return;

    // Clear existing items
    dropdown.innerHTML = '';

    // Add categories
    categories.forEach((category, index) => {
        const li = document.createElement('li');
        li.innerHTML = `
            <a class="dropdown-item" href="/${category.name}/new">
                <i class="${category.icon} me-2"></i>${category.displayName}
            </a>
        `;
        dropdown.appendChild(li);

        // Add divider after income category
        if (category.name === 'income' && index < categories.length - 1) {
            const divider = document.createElement('li');
            divider.innerHTML = '<hr class="dropdown-divider">';
            dropdown.appendChild(divider);
        }
    });
}
