import { Router } from 'express';
import { CategoryController } from '../controllers/categoryController';
import { requireAuth } from '../middlewares/auth';

const router = Router();

// Apply authentication middleware to all routes
router.use(requireAuth);

// Category CRUD routes
router.get('/', CategoryController.getAll);
router.get('/:id', CategoryController.getById);
router.post('/', CategoryController.create);
router.put('/:id', CategoryController.update);
router.delete('/:id', CategoryController.delete);

// Special routes
router.post('/reorder', CategoryController.reorder);

export default router;
