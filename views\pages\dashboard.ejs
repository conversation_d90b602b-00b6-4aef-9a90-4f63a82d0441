<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="refreshData">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-plus"></i> Quick Add
                    </button>
                    <ul class="dropdown-menu" id="dashboardQuickAddDropdown">
                        <!-- Categories will be loaded dynamically -->
                        <li><a class="dropdown-item" href="/incomes/new">Income</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/needs/new">Need</a></li>
                        <li><a class="dropdown-item" href="/wants/new">Want</a></li>
                        <li><a class="dropdown-item" href="/investments/new">Investment</a></li>
                        <li><a class="dropdown-item" href="/donations/new">Donation</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4" id="summaryCards">
    <!-- Cards will be populated by JavaScript -->
</div>

<!-- Budget Overview -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Budget Allocation
                </h5>
            </div>
            <div class="card-body">
                <canvas id="budgetChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Budget Status
                </h5>
            </div>
            <div class="card-body" id="budgetStatus">
                <!-- Status will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Category Breakdown -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Category Breakdown
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="categoryBreakdown">
                    <!-- Category cards will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Trends -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>Monthly Trends
                </h5>
            </div>
            <div class="card-body">
                <canvas id="trendsChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Transactions
                </h5>
                <a href="/transactions" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="recentTransactions">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Category</th>
                                <th>Description</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Transactions will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/js/dashboard.js"></script>
