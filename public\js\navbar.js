// Navbar dynamic category loading
document.addEventListener('DOMContentLoaded', function() {
    loadCategoriesForNavbar();
});

async function loadCategoriesForNavbar() {
    try {
        const response = await fetch('/api/categories');
        if (!response.ok) {
            // If categories API fails, keep the default hardcoded categories
            return;
        }
        
        const data = await response.json();
        const categories = data.data || [];
        
        if (categories.length === 0) {
            // No categories found, keep default
            return;
        }
        
        updateNavbarDropdown(categories);
        
    } catch (error) {
        console.error('Error loading categories for navbar:', error);
        // Keep default categories on error
    }
}

function updateNavbarDropdown(categories) {
    const dropdown = document.getElementById('addTransactionDropdown');
    if (!dropdown) return;
    
    // Clear existing items
    dropdown.innerHTML = '';
    
    // Add categories
    categories.forEach((category, index) => {
        const li = document.createElement('li');
        li.innerHTML = `
            <a class="dropdown-item" href="/${category.name}/new">
                <i class="${category.icon} me-2"></i>${category.displayName}
            </a>
        `;
        dropdown.appendChild(li);
        
        // Add divider after income category
        if (category.name === 'income' && index < categories.length - 1) {
            const divider = document.createElement('li');
            divider.innerHTML = '<hr class="dropdown-divider">';
            dropdown.appendChild(divider);
        }
    });
}
