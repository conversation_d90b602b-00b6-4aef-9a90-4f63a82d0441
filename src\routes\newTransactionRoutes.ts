import { Router } from 'express';
import { NewTransactionController } from '../controllers/newTransactionController';
import { requireAuth } from '../middlewares/auth';

const router = Router();

// Apply authentication middleware to all routes
router.use(requireAuth);

// Transaction CRUD routes
router.get('/', NewTransactionController.getAll);
router.get('/:id', NewTransactionController.getById);
router.post('/', NewTransactionController.create);
router.put('/:id', NewTransactionController.update);
router.delete('/:id', NewTransactionController.delete);

// Category-specific routes for backward compatibility
router.get('/category/:categoryName', NewTransactionController.getByCategoryName);

export default router;
